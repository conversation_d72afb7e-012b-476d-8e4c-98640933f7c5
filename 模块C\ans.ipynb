import os
from PIL import Image
import json
from modelscope import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import torch

base_dir = '~/模块C/resource/药品信息'
resized_dir = '~/模块C/resource/药品信息/resized_512'
model_dir = "~/模块C/resource/Qwen2-VL-2B-Instruct"
os.makedirs(resized_dir, exist_ok=True)

model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype="auto", device_map="auto")
processor = AutoProcessor.from_pretrained(model_dir)

# <1> resize函数：将图片 resize 到合适大小 两处代码共1.5分 
def resize_image(input_path, output_path, max_size=512):
    try:
        img = Image.open(input_path)
        img = img.convert("RGB")
        img.thumbnail((max_size, max_size), Image.ANTIALIAS)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        img.save(output_path, format='JPEG')
        return True
    except Exception as e:
        print(f"Failed to process {input_path}: {e}")
        return False

train_data = []

# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分

for sub in os.listdir(base_dir):
    sub_path = os.path.join(base_dir, sub)
    if not os.path.isdir(sub_path):
        continue

    imgs = [fname for fname in sorted(os.listdir(sub_path))
            if fname.lower().endswith(('.jpeg'))]
    if not imgs:
        continue

    front_candidates = ['1.jpeg']
    front_name = None
    for name in imgs:
        if name in front_candidates:
            front_name = name
            break
    if front_name is None:
        front_name = imgs[0]

    
    resized_paths_all = []
    resized_front_path = None

    for name in imgs:
        orig_path = os.path.join(sub_path, name)
        
        out_sub_dir = os.path.join(resized_dir, sub)
        os.makedirs(out_sub_dir, exist_ok=True)
        out_path = os.path.join(out_sub_dir, name)
        success = resize_image(orig_path, out_path, max_size=512)
        if success:
            uri = f"file://{out_path}"
            resized_paths_all.append(uri)
            if name == front_name:
                resized_front_path = uri

    
    if resized_front_path is None:
        print(f"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过")
        continue

# <3> 构造 messages，模型多图推理 4处代码共2分
    user_instruction = "请提取此药品的关键信息：名称、成分、用法用量、适应症、不良反应等。"
    content = []
    for p in resized_paths_all:
        content.append({"type": "image", "image": p})
    content.append({"type": "text", "text": user_instruction})
    messages = [
        {
            "role": "user",
            "content": content
        }
    ]
    
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    image_inputs, video_inputs = process_vision_info(messages)
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt"
    )
    inputs = inputs.to(model.device)
    
    with torch.no_grad():
        generated_ids = model.generate(**inputs, max_new_tokens=256)
    
    generated_ids_trimmed = [
        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    output_texts = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False
    )
    answer = output_texts[0].strip()

# <4> 构造并保存训练集，3处代码共1.5分
    entry = {
        "conversations": [
            {"from": "human", "value": user_instruction},
            {"from": "gpt", "value": answer}
        ],
        "images": [resized_front_path]
    }
    train_data.append(entry)


output_json_path = os.path.join(base_dir, 'train_data.json')
with open(output_json_path, 'w', encoding='utf-8') as f:
    json.dump(train_data, f, ensure_ascii=False, indent=2)

print(f"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}")

# <5>  在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，并将新生成的 train.json 放入 LLaMA-Factory/data 中。共1分。

# <6>  填充examples/train_lora/qwen2vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。

### model
model_name_or_path: Qwen/Qwen2.5-VL-7B-Instruct
image_max_pixels: 262144
video_max_pixels: 16384
trust_remote_code: true

### method
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_target: all

### dataset
dataset: mllm_demo,identity,alpaca_en_demo  
template: qwen2_vl
cutoff_len: 2048
max_samples: 1000
overwrite_cache: true
preprocessing_num_workers: 16
dataloader_num_workers: 4

### output
output_dir: saves/qwen2_5vl-7b/lora/sft
logging_steps: 10
save_steps: 500
plot_loss: true
overwrite_output_dir: true
save_only_model: false
report_to: none  

### train
per_device_train_batch_size: 1
gradient_accumulation_steps: 8
learning_rate: 1.0e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
ddp_timeout: 180000000
resume_from_checkpoint: null

### eval
# val_size: 0.1
# per_device_eval_batch_size: 1
# eval_strategy: steps
# eval_steps: 500

# <7> 开始训练 llamafactory-cli train examples/train_lora/qwen2vl_lora_sft.yaml 

# <8> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分

### model
model_name_or_path: ~/模块C/resource/Qwen2-VL-2B-Instruct
adapter_name_or_path: ~/模块C/ans/sft_model
template: qwen2_vl
trust_remote_code: true

### export
export_dir: ~/模块C/ans/qwen2vl_lora_sft
export_size: 5
export_device: auto  
export_legacy_format: false

# <9> 根据 Accuracy 及 规范输出数量 进行打分
# 相同分数情况下可比较规范输出数量

# <10> 参考 "~/模块C/resource/rknn-llm/tree/main/examples/Qwen2-VL_Demo"文档进行模型转换和部署推理 共3分
# 模型转换（rknn、rkllm转换成功 1分）
# 模型编译 1分
# 模型部署、推理 1分

# <11> 每题0.5分 共2.5分